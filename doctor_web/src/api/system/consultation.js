import request from '@/utils/request'

// 查询会诊医生明细列表
export function listDoctor(query) {
  return request({
    url: '/system/doctor/list',
    method: 'get',
    params: query
  })
}

// 查询会诊医生明细详细
export function getDoctor(id) {
  return request({
    url: '/system/doctor/' + id,
    method: 'get'
  })
}

// 新增会诊医生明细
export function addDoctor(data) {
  return request({
    url: '/system/doctor',
    method: 'post',
    data: data
  })
}

// 修改会诊医生明细
export function updateDoctor(data) {
  return request({
    url: '/system/doctor',
    method: 'put',
    data: data
  })
}

// 删除会诊医生明细
export function delDoctor(id) {
  return request({
    url: '/system/doctor/' + id,
    method: 'delete'
  })
}
