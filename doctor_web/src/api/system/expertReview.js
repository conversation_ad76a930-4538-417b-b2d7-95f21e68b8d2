import request from '@/utils/request'

// 查询会诊管理列表
export function listMeeting(query) {
  return request({
    url: '/system/meeting/list',
    method: 'get',
    params: query
  })
}

// 查询会诊管理详细
export function getMeeting(id) {
  return request({
    url: '/system/meeting/' + id,
    method: 'get'
  })
}

// 新增会诊管理
export function addMeeting(data) {
  return request({
    url: '/system/meeting',
    method: 'post',
    data: data
  })
}

// 修改会诊管理
export function updateMeeting(data) {
  return request({
    url: '/system/meeting',
    method: 'put',
    data: data
  })
}

// 删除会诊管理
export function delMeeting(id) {
  return request({
    url: '/system/meeting/' + id,
    method: 'delete'
  })
}
