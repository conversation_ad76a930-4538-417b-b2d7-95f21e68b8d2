<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="患者名称：" prop="patientName">
        <el-input
          v-model="queryParams.patientName"
          placeholder=""
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery">查询</el-button>
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作提示框 -->
    <div class="operation-tips">
      <div class="tips-content">
        <p><strong>1. 发起后，可查看专家审核状态</strong></p>
        <p><strong>2. 补充会诊：只有医生发起人才可查看补充会诊按钮。</strong></p>
        <p
          ><strong>3. 当会议结束后，补充会诊按钮显示，发起人可进行会诊信息补充，主要针对内容为会诊记录及报告</strong></p
        >
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="consultationList" class="consultation-table">
      <el-table-column label="序号" align="center" width="60">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="患者名称" align="center" prop="patientName" />
      <el-table-column label="疾病种类" align="center" prop="diseaseType" />
      <el-table-column label="会议类型" align="center" prop="meetingType">
        <template slot-scope="scope">
          <span>{{ scope.row.meetingType === "online" ? "线上" : "线下" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预期会议开始时间" align="center" prop="expectedStartTime" width="150" />
      <el-table-column label="预期会议结束时间" align="center" prop="expectedEndTime" width="150" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="120" />
      <el-table-column label="添加会议时间" align="center" prop="addMeetingTime" width="120" />
      <el-table-column label="会议地址/链接" align="center" prop="meetingAddress" />
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" class="expert-btn" @click="handleViewExperts(scope.row)"
            >查看专家</el-button
          >
          <el-button size="mini" type="text" class="supplement-btn" @click="handleSupplement(scope.row)"
            >补充会诊</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "Consultation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊列表数据
      consultationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: null,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊列表 */
    getList() {
      this.loading = true;
      // 模拟数据，实际应该调用API
      setTimeout(() => {
        this.consultationList = [
          {
            id: 1,
            patientName: "李明凯",
            diseaseType: "结构缺陷",
            meetingType: "online",
            expectedStartTime: "",
            expectedEndTime: "",
            auditTime: "",
            addMeetingTime: "",
            meetingAddress: "",
          },
          {
            id: 2,
            patientName: "",
            diseaseType: "",
            meetingType: "offline",
            expectedStartTime: "",
            expectedEndTime: "",
            auditTime: "",
            addMeetingTime: "",
            meetingAddress: "",
          },
        ];
        this.total = this.consultationList.length;
        this.loading = false;
      }, 500);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查看专家 */
    handleViewExperts(row) {
      this.$message.info("查看专家功能");
    },
    /** 补充会诊 */
    handleSupplement(row) {
      this.$message.info("补充会诊功能");
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

/* 操作提示框样式 */
.operation-tips {
  margin: 20px 0;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  position: relative;
}

.tips-content {
  padding: 15px 20px;

  p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
    color: #856404;

    strong {
      font-weight: 600;
    }
  }
}

/* 表格样式 */
.consultation-table {
  margin-top: 20px;
  border: 1px solid #e4e7ed;

  ::v-deep .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  ::v-deep .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
    border-bottom: 1px solid #e4e7ed;
  }

  ::v-deep .el-table__body tr:hover > td {
    background-color: #f5f7fa;
  }

  ::v-deep .el-table td {
    border-bottom: 1px solid #e4e7ed;
    padding: 12px 0;
  }
}

/* 操作按钮样式 */
.expert-btn {
  color: #409eff;
  margin-right: 10px;

  &:hover {
    color: #66b1ff;
  }
}

.supplement-btn {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

/* 搜索表单样式 */
::v-deep .el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 分页样式 */
::v-deep .el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
