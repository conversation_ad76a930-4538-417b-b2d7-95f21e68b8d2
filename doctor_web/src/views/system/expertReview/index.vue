<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="病人id" prop="memId">
        <el-input v-model="queryParams.memId" placeholder="请输入病人id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="预期会议开始时间" prop="expectStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.expectStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择预期会议开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="预期会议结束时间" prop="expectEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.expectEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择预期会议结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="会议地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入会议地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核时间(记录最后审核通过时间)" prop="auditTime">
        <el-date-picker
          clearable
          v-model="queryParams.auditTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审核时间(记录最后审核通过时间)"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="queryParams.remarks" placeholder="请输入备注" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="创建人" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最后更新人" prop="updataUser">
        <el-input
          v-model="queryParams.updataUser"
          placeholder="请输入最后更新人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最后更新时间" prop="updataTime">
        <el-date-picker
          clearable
          v-model="queryParams.updataTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择最后更新时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:meeting:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:meeting:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:meeting:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:meeting:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="meetingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会议id" align="center" prop="id" />
      <el-table-column label="病人id" align="center" prop="memId" />
      <el-table-column label="会议类型 0-线上  1-线下" align="center" prop="consultType" />
      <el-table-column label="预期会议开始时间" align="center" prop="expectStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectStartTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预期会议结束时间" align="center" prop="expectEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectEndTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会议地址" align="center" prop="address" />
      <el-table-column label="会议记录" align="center" prop="record" />
      <el-table-column label="审核时间(记录最后审核通过时间)" align="center" prop="auditTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态 1-已审核 0-待审核" align="center" prop="auditStatus" />
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="创建人" align="center" prop="createUser" />
      <el-table-column label="最后更新人" align="center" prop="updataUser" />
      <el-table-column label="最后更新时间" align="center" prop="updataTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updataTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meeting:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:meeting:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会诊管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="病人id" prop="memId">
          <el-input v-model="form.memId" placeholder="请输入病人id" />
        </el-form-item>
        <el-form-item label="预期会议开始时间" prop="expectStartTime">
          <el-date-picker
            clearable
            v-model="form.expectStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择预期会议开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预期会议结束时间" prop="expectEndTime">
          <el-date-picker
            clearable
            v-model="form.expectEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择预期会议结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="会议地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入会议地址" />
        </el-form-item>
        <el-form-item label="会议记录" prop="record">
          <el-input v-model="form.record" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="审核时间(记录最后审核通过时间)" prop="auditTime">
          <el-date-picker
            clearable
            v-model="form.auditTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审核时间(记录最后审核通过时间)"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="最后更新人" prop="updataUser">
          <el-input v-model="form.updataUser" placeholder="请输入最后更新人" />
        </el-form-item>
        <el-form-item label="最后更新时间" prop="updataTime">
          <el-date-picker
            clearable
            v-model="form.updataTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择最后更新时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMeeting, getMeeting, delMeeting, addMeeting, updateMeeting } from "@/api/system/expertReview";

export default {
  name: "ExpertReview",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊管理表格数据
      meetingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        memId: null,
        consultType: null,
        expectStartTime: null,
        expectEndTime: null,
        address: null,
        record: null,
        auditTime: null,
        auditStatus: null,
        remarks: null,
        createUser: null,
        updataUser: null,
        updataTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊管理列表 */
    getList() {
      this.loading = true;
      listMeeting(this.queryParams).then((response) => {
        this.meetingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        memId: null,
        consultType: null,
        expectStartTime: null,
        expectEndTime: null,
        address: null,
        record: null,
        auditTime: null,
        auditStatus: null,
        remarks: null,
        createUser: null,
        updataUser: null,
        updataTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会诊管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMeeting(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会诊管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMeeting(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeeting(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除会诊管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMeeting(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/meeting/export",
        {
          ...this.queryParams,
        },
        `meeting_${new Date().getTime()}.xlsx`,
      );
    },
  },
};
</script>
